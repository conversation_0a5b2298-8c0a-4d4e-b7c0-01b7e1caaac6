***Prompt Expert Neuro-Algorithmique V.4.6***

**\*(Hybride CSP++/ToT/CoVe/ CRISPE++**   
**Auto-Adaptation Contextuelle \+ Algorithmes Dynamiques) \***

---

**Instructions-Prompts: Méthodologie Avancée**   
**pour la Résolution de Problèmes Complexes**

Introduction:   
Ce guide fournit une méthodologie structurée pour analyser et résoudre des problèmes complexes en utilisant des techniques avancées d'ingénierie des prompts et des algorithmes de recherche. Il est conçu pour être utilisé en conjonction avec des modèles de langage (LLM) tels que ChatGPT, Gemini, Claude, etc. **Pour une approche encore plus puissante, la technique du Raisonnement Séquentiel Mixte (RSM) peut être employée.** Le RSM combine plusieurs méthodes d'invite et catégories d'algorithmes pour créer des prompts nouvelle génération, guidant le modèle de langage dans un raisonnement complexe, étape par étape.

## "Aide à la définition des paramètres":

### **aider l'utilisateur à clarifier chaque paramètre:**

* #### Types de problèmes:

  * "Quel est le résultat que vous souhaitez obtenir?  
  * S'agit-il de trouver la meilleure solution parmi plusieurs options (optimisation)?  
  * De prédire un événement futur (prédiction)?  
  * De classer des éléments en catégories (classification)?  
  * De générer de nouvelles idées (génération d'idées)?  
  * D'explorer différentes solutions possibles (exploration de solutions)?"  
  * Des exemples concrets pour chaque type de problème seraient également fournis, comme ceux que j'ai mentionnés précédemment.

#### Contraintes:

* "Quelles sont les limitations ou les restrictions qui s'appliquent à votre problème?

* Avez-vous des contraintes de temps, de budget, de ressources humaines, techniques, éthiques, environnementales ou sociales?"

* Des exemples de contraintes pour chaque catégorie seront également proposés. \\

* **Objectifs:**

  * "Qu'est-ce que vous espérez accomplir en résolvant ce problème?  
  * Quels sont les résultats mesurables que vous souhaitez atteindre?"  
  * Des exemples d'objectifs seraient également fournis, en lien avec différents domaines (business, social, environnemental, etc.).

\*\* Intégration de cette aide dans le processus de prompting:\*\*

Lorsque l'utilisateur demande de l'aide pour définir les paramètres, le système pourrait lui poser des questions ciblées pour l'aider à clarifier ses besoins.

Par exemple:

* "Pouvez-vous me donner plus de détails sur votre problème?"  
* "Quels sont les éléments les plus importants à prendre en compte?"  
* "Avez-vous déjà envisagé certaines solutions?"  
* "Quelles sont vos priorités?"

# Phases de la Résolution de Problèmes

## Adaptation dynamique de la résolution

Ce système est capable de s'adapter à la complexité du problème pour choisir la méthode et la chaîne de raisonnement les plus efficaces.

### **Évaluation de la complexité**

* Le système analyse le problème en fonction de critères tels que :  
  * le nombre de variables et de contraintes,  
  * le nombre d'étapes nécessaires,  
  * le niveau d'ambiguïté,  
  * le domaine de connaissance requis.

  ### **Sélection de la méthode et de la chaîne de raisonnement**

#### Problèmes simples

* **CoT simplifié**  
* **Prompt direct**  
* **Knowledge-Enriched Prompting**  
* Chaîne de raisonnement simplifiée en 3 étapes.

#### Problèmes complexes

* **CoT**  
* **CSP**  
* **ToT**  
* **RSM**  
* **DFS**  
* **BFS**  
* Chaîne de raisonnement complète avec plusieurs phases.

  ### **Ajustement dynamique**

Le système peut réévaluer la complexité du problème et ajuster la méthode et la chaîne de raisonnement si nécessaire.

1. ## Si Problème complexe rencontré à résoudre :

2. ### **Raisonnement Séquentiel Mixte (RSM)**

3. **Description :**  
   Le RSM est une technique de prompting avancée qui combine plusieurs méthodes d'invite et catégories d'algorithmes pour créer des prompts nouvelle génération. Elle vise à guider le modèle de langage dans un raisonnement complexe, étape par étape, afin de résoudre des problèmes complexes en décomposant le processus en étapes distinctes et en appliquant les techniques les plus appropriées à chaque étape.

4. **Méthodes :**

   1. **Chain-of-Thought (CoT) :**  
      Décompose le problème en étapes et guide le modèle dans son raisonnement.  
   2. **Constraint Satisfaction Problem (CSP) :**  
      Modélise les contraintes et les variables du problème.  
   3. **Tree-of-Thoughts (ToT) :**  
      Explore les solutions possibles via une approche arborescente.  
   4. \#\# Formalisation des transitions inter-étapes  

      Définir une fonction de transition \\( T: S\_i \\rightarrow S\_{i+1} \\) où :  

      \- \\( S\_i \\) \= État courant du raisonnement  

      \- \\( T \= f(\\text{Contexte}, \\text{Contraintes}, \\text{Objectifs}) \\)  

      Implémenter une vérification de cohérence :  

      \\\[ \\forall (s, s') \\in T,\\ \\text{Valid}(s) \\Rightarrow \\text{Valid}(s') \\\]

   5. **Knowledge-Enriched Prompting :**  
      Enrichir le prompt avec des informations pertinentes.

5. **Synergies et Combos Possibles :**

   - **CoT \+ CSP :** Guider le raisonnement en tenant compte des contraintes.  
   - **ToT \+ DFS/BFS :** Explorer l'arbre des solutions de manière efficace.  
   - **Knowledge-Enriched Prompting \+ CoT :** Fournir un contexte riche pour chaque étape du raisonnement.

* **Chaîne de raisonnement :**

  1. **Définition du problème :** Décrire le problème et ses composantes.  
  2. **Modélisation CSP :** Identifier les variables et les contraintes.  
  3. **Exploration ToT :** Générer des solutions potentielles.  
  4. **Raisonnement CoT :** Évaluer et affiner les solutions.  
  5. **Validation :** Vérifier la solution finale par rapport aux contraintes et aux objectifs.

* **Exemples d'utilisation :**  
  1. "**En utilisant le Raisonnement Séquentiel Mixte (RSM)**, résolvez le problème suivant :   
     **\[Question de l’utilisateur, description du problème\]**.  
  2. **Modélisez le problème en utilisant CSP.**  
  3. **Explorez les solutions possibles avec ToT et la recherche en profondeur (DFS).**  
  4. **Guidez le modèle dans son raisonnement avec CoT.**  
  5. **\*\*Enrichissez le prompt avec des informations pertinentes sur le domaine du problème."**

## 1\. Définition du Problème (CSP, ToT)

* ### **Description**

  * Décrivez le problème en identifiant les acteurs impliqués, les contraintes majeures (sociales, économiques, environnementales) et les objectifs attendus.  
    ---

    * ### **Méthodes**

  * **Représentation Tensorielle :**  
    * \- Décomposition CP/Tucker pour la compression des graphes de connaissances  
    * \- Contractions tensorielles pour les requêtes multi-relationnelles : \\( \\mathcal{X} \\times\_1 U \\times\_2 V \\times\_3 W \\)\[3\]  
    * \- Régularisation tensorielle pour éviter l'overfitting

  ---

  * #### **Constraint Satisfaction Problem (CSP)**

    * Modélisez le problème en termes de variables et de contraintes.  
    * Variables: x1, x2,..., xn  
    * Domaines: D1, D2,..., Dn  
    * Contraintes: C1, C2,..., Cm  
    * Solution: Une affectation de valeurs aux variables qui satisfait toutes les contraintes  
    * Description: CSP permet de formaliser un problème de manière précise.

    ---

  * #### **Tree-of-Thoughts (ToT)**

    * Explorez les solutions possibles en utilisant une approche arborescente.  
    * Arbre: Structure de données avec un nœud racine, des nœuds internes et des nœuds feuilles  
    * Nœuds: Représentent les états possibles du problème  
    * Transitions: Connexions entre les nœuds, représentant les actions possibles  
    * Solution: Un chemin de la racine à un nœud feuille qui représente une solution au problème  
    * Description: ToT permet d'explorer différentes solutions de manière structurée.

    ---

  * #### **Zero-Shot Learning**

    * Décrivez le problème et demandez directement au modèle de proposer des solutions, sans fournir d'exemples spécifiques.  
    * Description: Utile pour explorer des solutions innovantes et inattendues, en s'appuyant sur la capacité du modèle à généraliser

    ---

    * #### **Analogical Prompting**

    * Décrivez un problème analogue et demandez au modèle de s'en inspirer pour résoudre le problème actuel.  
    * Description: Utile pour transférer des connaissances d'un domaine à un autre et stimuler la créativité en identifiant des solutions inattendues

    ---

    * ### **Synergies et Combos Possibles**

    * **CSP \+ CoT** : Guider le raisonnement en respectant les contraintes  
    * **ToT \+ Knowledge-Enriched Prompting** : Fournir au modèle des informations sur les états et les transitions  
    * Combinez CSP et ToT pour identifier les solutions qui satisfont les contraintes tout en explorant un large éventail de possibilités  
    * Utilisez la Recherche en Profondeur (DFS) avec ToT pour analyser en détail les branches prometteuses  
    * Intégrez le Chain-of-Knowledge (CoK) pour enrichir le contexte avec des informations pertinentes sur les contraintes et les solutions possibles  
    * **CSP \+ Zero-Shot Learning** : Demandez au modèle de proposer des solutions qui respectent les contraintes du problème, sans lui fournir d'exemples  
    * **ToT \+ Zero-Shot Learning** : Combinez l'exploration arborescente avec la génération de solutions zero-shot pour élargir l'espace des solutions explorées  
      *Exemple* : "Un nouveau virus se propage rapidement. En utilisant Zero-Shot Learning, proposer des stratégies innovantes pour enrayer l'épidémie, en tenant compte des contraintes sociales et économiques."  
    * **CSP \+ Analogical Prompting** : Utilisez un problème analogue pour identifier les variables et les contraintes du problème actuel  
    * **ToT \+ Analogical Prompting** : Utilisez un problème analogue pour guider l'exploration de l'arbre des solutions  
      *Exemple* : "Une équipe doit concevoir un nouveau système de ventilation pour un bâtiment. Utilisez Analogical Prompting en demandant au modèle de s'inspirer du système respiratoire humain pour proposer des solutions innovantes."

  


## 2\. Diagnostic Initial (Analyse Pareto, GoT)

* **Description:** Identifiez les causes profondes du problème à l'aide d'analyses systémiques (TRIZ, Six Sigma). Déterminez les principaux obstacles et contradictions.

* **Méthodes:**

  * **\*\*Analyse Pareto: \*\***Identifiez les facteurs les plus influents sur le problème.

    * Loi de Pareto: y \= A / x^α  
    * Principe de Pareto (80/20): 80% des effets sont produits par 20% des causes.  
    * Description:  Permet de concentrer les efforts sur les causes les plus importantes. \\

  * **\*\*Chain-of-Verification (CoVe): \*\***Break down the diagnostic process into steps, where each step verifies the output of the previous step. \\

  * **Graph-of-Thoughts (GoT):** Représentez graphiquement les relations entre les différents aspects du problème.

    * Matrices d'adjacence, mesures de centralité.  
    * Description:  Analyse la structure du problème et identifie les éléments clés. \\

  * **Plan-and-Solve Prompting:** Décrivez le problème et demandez au modèle de générer un plan pour le résoudre, puis de mettre en œuvre ce plan.

    Description:  Utile pour les problèmes complexes qui nécessitent une planification avant l'action. Permet de décomposer le problème en étapes et de s'assurer que chaque étape est résolue avant de passer à la suivante.

* **Synergies et Combos Possibles:**

  * **Analyse Pareto \+ CoT:**  Résoudre les causes les plus importantes du problème étape par étape.

  * **GoT \+ Knowledge-Enriched Prompting:**  Fournir au modèle une représentation structurée des connaissances....

  * **Combinez GoT avec la Recherche en Largeur (BFS)** pour explorer tous les aspects du problème de manière systématique.

  * **Intégrez le Chain-of-Verification (CoVe)** pour valider les conclusions de l'analyse.

  * **Analyse Pareto \+ Plan-and-Solve Prompting:**  Utilisez l'analyse Pareto pour identifier les causes les plus importantes du problème, puis demandez au modèle de générer un plan pour les résoudre.

  * **Combine CoVe with the Pareto Analysis** to verify the accuracy of the identified factors.

  * **Integrate CoVe with GoT** to ensure the consistency and validity of the relationships between different aspects of the problem.

    Example:

    * Identify the initial symptoms of the problem.  
    * Verify the accuracy and completeness of the identified symptoms.  
    * Analyze the potential causes of the symptoms.  
    * Verify the plausibility and relevance of the potential causes.  
    * Investigate the root causes of the problem.  
    * Verify the validity and accuracy of the root causes....

  * **GoT \+ Plan-and-Solve Prompting:**  Représenter les connaissances sur le problème sous forme de graphe (GoT), puis utiliser **Plan-and-Solve Prompting** pour générer un plan de résolution.   
    Exemple:"Une entreprise souhaite améliorer la satisfaction de ses clients. Utilisez Plan-and-Solve Prompting en demandant au modèle de:

    1. Identifier les causes de l'insatisfaction.

    2. Proposer un plan d'action pour améliorer chaque aspect.

    3. Évaluer l'impact potentiel de chaque action."...

## 3\. Exploration Arborescente (ToT)

* **Description:** Générez plusieurs scénarios possibles en utilisant ToT.    
  Évaluez leur faisabilité et leurs impacts.  
* **Méthodes:**  
  * Tree-of-Thoughts (ToT)  
* **Synergies et Combos Possibles:**

  * **ToT \+ DFS/BFS:** Explorer l'arbre des solutions de manière efficace.

    * **Formule (DFS):**

      DFS(noeud):

      Si noeud est un objectif:

        Retourner Solution(noeud)

      Pour chaque enfant de noeud:

        resultat \= DFS(enfant)

        Si resultat n'est pas nul:

          Retourner resultat

      Retourner nul

    * **Formule (BFS):**

      BFS(graphe, noeud\_depart):

      creer une file d'attente 

      enfiler noeud\_depart dans Q

      marquer noeud\_depart comme visité

      tant que Q n'est pas vide:

        noeud\_courant \= défiler Q

        pour chaque voisin v de noeud\_courant:

          si v n'est pas visité:

            enfiler v dans Q

            marquer v comme visité

| Critère | DFS | BFS | Hybride DFS/BFS |
| :---- | :---- | :---- | :---- |
| Complexité spatiale | ( O(bm) ) | ( O(b^d) ) | ( O(b^{d/2}) ) |
| Optimalité | Non | Oui | Conditionnelle |
| Cas d'usage | Solutions profondes | Solutions larges | Grands espaces |

    * **Description:** La recherche en profondeur (DFS) explore chaque branche de l'arbre en profondeur avant de passer à la branche suivante, tandis que la recherche en largeur (BFS) explore tous les nœuds à un niveau donné avant de passer au niveau suivant.Le choix entre DFS et BFS dépend de la structure de l'arbre et des objectifs de la recherche.   
       \\

  * **\*\*ToT \+ Scaffolding Prompting: \*\***Décomposer le problème en sous-tâches et fournir des instructions ou des exemples pour chaque sous-tâche.

    * Description:  Guider le modèle dans l'exploration de l'arbre des solutions en fournissant un soutien étape par étape.  
    * Exemple:"Un élève doit résoudre un problème mathématique complexe. Utilisez Scaffolding Prompting en décomposant le problème en étapes et en fournissant des indices ou des exemples pour chaque étape. Utilisez ToT pour explorer les différentes options à chaque étape."...

  * **ToT \+ Algorithme de Dijkstra:** Trouver le chemin le plus court vers la solution optimale dans l'arbre des solutions.

    * **Formule:** Fonction de coût pour chaque nœud, mise à jour itérative des distances.  
    * **Description:** L'algorithme de Dijkstra permet de calculer la distance minimale entre un nœud de départ et tous les autres nœuds de l'arbre.   
      Il est particulièrement utile lorsque les "coûts" de transition entre les nœuds sont différents.

  * **ToT \+ Monte Carlo Search:** Simuler les différents scénarios et évaluer leur probabilité de succès.

    * **Formule:**  estimation de la valeur d'un nœud par moyenne des résultats des simulations.  
    * **Description:** La recherche Monte Carlo utilise des simulations aléatoires pour estimer la valeur de chaque nœud dans l'arbre des solutions.   
      Elle est particulièrement utile lorsque l'espace des solutions est très grand.

  * **\*\*ToT \+  Théorie des probabilités: \*\***Calculer la probabilité de chaque scénario et identifier les plus probables.

    * **Formules:** Probabilité conditionnelle, Théorème de Bayes, Distributions de probabilité.  
    * **Description:**  En combinant ToT avec la théorie des probabilités, il est possible de quantifier l'incertitude associée à chaque branche de l'arbre et de prendre des décisions plus éclairées.... \\

  * **\*\*ToT \+ Least-to-Most Prompting: \*\***Décomposer le problème en sous-problèmes de difficulté croissante et utiliser ToT pour explorer les solutions de chaque sous-problème.

  * Description:  Permet de guider le modèle vers des solutions complexes en commençant par des étapes plus simples et en augmentant progressivement la difficulté.

  * **ToT \+ Prompt Chaining:** Enchaîner plusieurs prompts pour décomposer le problème en étapes et guider le modèle vers une solution finale.

    * Description:  Permet de combiner les forces de différentes techniques de prompting et d'explorer l'espace des solutions de manière plus flexible.  
    * Exemple:"Un utilisateur souhaite écrire un poème sur un thème spécifique. Utilisez Prompt Chaining en commençant par un prompt pour générer des idées, puis un prompt pour structurer le poème, et enfin un prompt pour affiner le style et la rime."...

  * **ToT \+ Hierarchical Prompting:** Organiser les sous-tâches du problème en une hiérarchie et utiliser ToT pour explorer les solutions à chaque niveau de la hiérarchie.

    * Description:  Permet de décomposer des problèmes complexes en sous-problèmes plus simples et de les résoudre de manière structurée.  
    * Exemple:"Une équipe doit organiser un événement complexe. Utilisez Hierarchical Prompting en décomposant l'événement en sous-tâches (logistique, communication, programme) et en utilisant ToT pour explorer les solutions à chaque niveau de la hiérarchie."...

  * **ToT \+ Curriculum Prompting:** Présenter les sous-tâches du problème dans un ordre de difficulté croissante et utiliser ToT pour explorer les solutions à chaque niveau de difficulté.

    * Description:  Permet de guider le modèle vers des solutions complexes en commençant par des étapes plus simples et en augmentant progressivement la difficulté.  
    * Exemple:"Un robot doit apprendre à construire une structure complexe. Utilisez Curriculum Prompting en décomposant la tâche en étapes de difficulté croissante: 1\. Assembler des blocs simples. 2\. Construire des formes basiques. 3\. Créer des structures complexes. Utilisez ToT pour explorer les différentes options à chaque étape."...

  * **\*\*Appliquez Self-Consistency \*\***pour vérifier la cohérence des résultats entre les différentes branches de l'arbre.

  * **Intégrez Active Prompting** pour explorer dynamiquement les branches les plus prometteuses.

## 4\. Génération de Solutions  \\

(Algorithmes Génétiques, Adversarial Prompting)

* **Description:** Proposez des solutions innovantes basées sur les scénarios identifiés. Précisez les ressources nécessaires, les parties prenantes impliquées et les risques potentiels.

* **Méthodes:**

  * **\*\*Algorithmes Génétiques: \*\***Générez et optimisez les solutions en utilisant des techniques d'évolution artificielle.

    * Fonction d'adaptation (fitness): f(x)  
    * Opérateurs génétiques: Sélection, Croisement, Mutation.  
    * Description:  Simule l'évolution naturelle pour trouver des solutions optimales.

  * **\*\*Adversarial Prompting: \*\***Testez la robustesse des solutions en les confrontant à des scénarios difficiles.

    * Perturbation: δ  
    * Robustesse: Capacité du modèle à résister aux perturbations.  
    * Description:  Identifie les faiblesses du modèle en introduisant des perturbations. \\

  * **\*\*Few-Shot Learning: \*\***Fournissez au modèle un petit nombre d'exemples de solutions pour guider la génération de nouvelles solutions.

    * Description:  Utile lorsque des exemples pertinents sont disponibles, mais en nombre limité. Permet d'accélérer l'apprentissage et d'améliorer la qualité des solutions générées.

  * **Generate-and-Test Prompting:** Demandez au modèle de générer plusieurs solutions candidates, puis de les évaluer et de les comparer pour sélectionner la meilleure.

    * Description:  Utile pour explorer un large espace de solutions et identifier les plus prometteuses. Encourager la créativité et l'innovation en générant des solutions variées.

  * **Modélisation avancée :**  
    *"Pour des problèmes à relations non linéaires (ex : épidémiologie), générez des solutions via une régression polynomiale. Par exemple : 'Modélisez la propagation d’une maladie avec un polynôme de degré 2 pour identifier le pic épidémique.'"*


  

* ### **Classification Multiclase (Régression Logistique Multinomiale)**

Prédisez des catégories parmi ( K ) classes possibles en utilisant une généralisation de la régression logistique pour les problèmes multiclasses. La fonction **softmax** est utilisée pour normaliser les probabilités.

**Méthode :**

* **Formule Mathématique (Softmax) :**  
  \[ P(y=k \\mid \\mathbf{x}) \= \\frac{e^{\\boldsymbol{\\beta}*k \\cdot \\mathbf{x}}}{\\sum*{j=1}^K e^{\\boldsymbol{\\beta}\_j \\cdot \\mathbf{x}}} \]  
  * **Fonction de Coût (Entropie Croisée) :**  
    \[ \\mathcal{L} \= \-\\sum\_{i=1}^N \\sum\_{k=1}^K y\_{ik} \\ln\\left(P(y\_i=k \\mid \\mathbf{x}\_i)\\right) \]  
  * **Cas d'usage :**  
    Classification de textes (catégories d’articles), diagnostics médicaux (maladies multiples), segmentation client.

    **Prompt Exemple :**  
    *"Analysez le dataset \[lien\] de symptômes médicaux et de diagnostics. Entraînez un modèle de régression logistique multinomiale pour classer les patients dans l’une des 5 catégories de maladies. Interprétez les coefficients des variables clés."*

* ### **Classification et Régression par SVM (Support Vector Machines)**

  * Utilisez des SVM pour résoudre des problèmes de classification linéaire/non linéaire et de régression en maximisant la marge entre les classes. Adaptez-les aux données complexes via des **fonctions noyau**.  
  * **Méthodes :**  
  * **Hyperplan Optimal (Classification) :**  
    \[ \\mathbf{w} \\cdot \\mathbf{x} \+ b \= 0 \\quad \\text{avec} \\quad \\text{marge} \= \\frac{2}{|\\mathbf{w}|} \]  
    **Fonction de décision :**  
    \[ f(\\mathbf{x}) \= \\text{signe}(\\mathbf{w} \\cdot \\mathbf{x} \+ b) \]  
  * **Fonctions Noyau (Kernel Trick) :**  
    \[ K(\\mathbf{x}\_i, \\mathbf{x}\_j) \= \\phi(\\mathbf{x}\_i) \\cdot \\phi(\\mathbf{x}\_j) \]  
    Exemples :  
    * **Linéaire :** ( K(\\mathbf{x}\_i, \\mathbf{x}\_j) \= \\mathbf{x}\_i \\cdot \\mathbf{x}\_j )  
    * **RBF (Radial Basis Function) :** ( K(\\mathbf{x}\_i, \\mathbf{x}\_j) \= e^{-\\gamma |\\mathbf{x}\_i \- \\mathbf{x}\_j|^2} )  
    * **Polynomial :** ( K(\\mathbf{x}\_i, \\mathbf{x}\_j) \= (\\mathbf{x}\_i \\cdot \\mathbf{x}\_j \+ c)^d )  
  * **Cas d'usage :**  
  * Classification binaire/multiclasse (ex : détection de spam, reconnaissance d’images).  
  * Régression (SVR) pour prédire des valeurs continues avec une tolérance d’erreur ((\\epsilon)-tube).  
  * **Prompt Exemple (Classification) :**  
    *"Classifiez des emails en spam/non-spam avec un SVM linéaire. Utilisez TF-IDF pour vectoriser le texte et optimisez (C) (paramètre de régularisation) via validation croisée. Interprétez les vecteurs supports."*  
  * **Prompt Exemple (Régression) :**  
    *"Prédisez le prix des logements à partir de variables socio-économiques avec SVR (Support Vector Regression). Choisissez un noyau RBF et ajustez (\\gamma) pour minimiser l’erreur quadratique moyenne."*

* ### **Réseaux de Neurones Artificiels (RNA)**

  Modélisez des relations complexes via des architectures de neurones inspirées du cerveau humain. Utilisez des couches interconnectées avec des fonctions d’activation non linéaires pour apprendre des motifs hiérarchiques.  
  **Architecture et Formules :**

* **Propagation avant (Forward Pass) :**  
  \[ a^{(l)} \= \\sigma\\left( W^{(l)} \\cdot a^{(l-1)} \+ b^{(l)} \\right) \]

  * ( \\sigma ): Fonction d’activation (ex: ReLU, sigmoïde).  
  * ( W^{(l)}, b^{(l)} ): Poids et biais de la couche ( l ).

* **Fonctions d’Activation :**

  * **ReLU :** ( \\sigma(x) \= \\max(0, x) )  
  * **Sigmoïde :** ( \\sigma(x) \= \\frac{1}{1 \+ e^{-x}} )  
  * **Softmax (classification multiclasse) :**  
    \[ \\sigma(\\mathbf{z})*i \= \\frac{e^{z\_i}}{\\sum*{j=1}^K e^{z\_j}} \]

* **Fonction de Coût (Entropie Croisée) :**  
  \[ \\mathcal{L} \= \-\\frac{1}{N} \\sum\_{i=1}^N \\sum\_{k=1}^K y\_{ik} \\ln(\\hat{y}\_{ik}) \]

  * ( \\hat{y}\_{ik} ): Probabilité prédite pour la classe ( k ).

  **Cas d’usage :**

* Reconnaissance d’images (CNN), traitement du langage (RNN/Transformers), prédiction de séries temporelles (LSTM).  
  **Prompt Exemple (Classification) :**  
  *"Entraînez un réseau de neurones à 3 couches (128-64-10) sur le dataset MNIST. Utilisez ReLU et une couche Softmax finale. Visualisez les features maps de la première couche et évaluez l’accuracy."*

*![][image1]*

* **Synergies et Combos Possibles:**

  * **Intégrez avec Algorithmes Génétiques** pour optimiser l’architecture (nombre de couches, neurones).  
  * **Combinez avec Digital Twin**  pour simuler des défaillances de capteurs et tester la robustesse du RNA.  
  * **Validez avec Ethical Prompting**  pour détecter des biais dans les prédictions (ex: déséquilibres de classes).  
  * **Intégrez avec Algorithmes Génétiques** pour optimiser les hyperparamètres ((C, \\gamma, d)).  
  * **Combinez avec Digital Twin**  pour simuler l’impact de perturbations sur les vecteurs supports.  
  * **Validez avec Ethical Prompting** pour détecter des biais dans les décisions du modèle.  
  * **Intégrez avec Algorithmes Génétiques** pour optimiser la sélection de variables.  
  * **Combinez avec Adversarial Prompting** pour tester la robustesse du modèle à des perturbations de données.  
  * **Validez avec Ethical Prompting**  pour détecter des biais dans les prédictions multiclasses  
  * **Algorithmes génétiques \+ CoT:**  Explorer l'espace des solutions et évaluer les solutions.  
  * **Algorithmes génétiques \+ Generate-and-Test Prompting:**  Utilisez Generate-and-Test Prompting pour générer la population initiale des algorithmes génétiques.  
  * **Adversarial Prompting \+ Generate-and-Test Prompting:**  Utilisez Generate-and-Test Prompting pour générer des solutions candidates, puis utilisez Adversarial Prompting pour tester leur robustesse.  
    * Exemple:"Une entreprise souhaite développer une nouvelle stratégie marketing. Utilisez Generate-and-Test Prompting en demandant au modèle de:  
      1. Générer plusieurs stratégies marketing différentes.  
      2. Évaluer les avantages et les inconvénients de chaque stratégie.  
      3. Comparer les stratégies et recommander la meilleure."...  
  * **Adversarial Prompting \+ Counterfactual Prompting:**  Tester la robustesse des solutions en générant des scénarios alternatifs....  
  * **Combinez les Algorithmes Génétiques avec le Front de Pareto** pour optimiser les solutions en fonction de plusieurs critères.  
  * **Utilisez Adversarial Prompting avec la Recherche Monte Carlo** pour simuler des scénarios extrêmes.  
  * **Intégrez ReAct** pour tester les solutions en situation réelle.  
  * **Algorithmes génétiques \+ Few-Shot Learning:**  Initialiser la population des algorithmes génétiques avec des exemples de solutions **(few-shot)** pour accélérer la convergence.  
  * **Adversarial Prompting \+ Few-Shot Learning:**  Utilisez des exemples de solutions (few-shot) pour générer des exemples adversarial prompts plus pertinents.


  

## 5\. Validation Éthique et Sociale  \\

**(Ethical Prompting, Counterfactual Prompting)**

* **Description:** Validez chaque solution en fonction des Objectifs de Développement Durable (ODD) et des critères éthiques locaux. Identifiez les biais potentiels.

* **Méthodes:**

  * **\*\*Ethical Prompting: \*\***Guidez le modèle pour qu'il génère des solutions éthiques.

    * Fonctions utilitaires: U(x)  
    * Principes éthiques: Règles et normes morales formalisées.  
    * Description:  Intègre les considérations éthiques dans la génération de solutions.

  * **Counterfactual Prompting:** Explorez des scénarios alternatifs pour identifier les biais et les conséquences inattendues.

    * Scénario contrefactuel: x'  
    * Impact: Δy \= f(x') \- f(x)  
    * Description:  Analyse l'influence de différents facteurs et identifie les biais. \\

  * **Hypothetical Prompting:**  Posez des questions hypothétiques pour explorer les implications éthiques et sociales des solutions.

    * Description:  Utile pour identifier les biais, les risques et les opportunités potentiels des solutions. \\

  * **Explainable Prompting:** Demandez au modèle de justifier ses réponses et ses décisions.

    * Description:  Utile pour comprendre le raisonnement du modèle et identifier les biais potentiels.

* **Synergies et Combos Possibles:**  
  * **Ethical Prompting \+ Knowledge-Enriched Prompting:** Fournir au modèle des informations sur les principes éthiques.   
    * **\* Counterfactual Prompting \+ CoT:** Analyser les conséquences de différents scénarios contrefactuels étape par étape....  
  * Utilisez **Counterfactual Prompting avec la Recherche Monte Carlo** pour simuler l'impact des solutions sur différents groupes sociaux.  
  * **Intégrez Self-Consistency** pour vérifier la cohérence des solutions avec les valeurs éthiques.  
  * **Ethical Prompting \+ Hypothetical Prompting:**  Utilisez Hypothetical Prompting pour explorer les implications éthiques des solutions.  
  * **Counterfactual Prompting \+ Hypothetical Prompting:**  Utilisez Hypothetical Prompting pour générer des scénarios contrefactuels.  
    * Exemple:"Un système d'IA est utilisé pour la sélection des candidats à un emploi. Utilisez Hypothetical Prompting pour explorer les biais potentiels du système en posant des questions comme: 'Que se passerait-il si le système était entraîné sur un jeu de données biaisé?' ou 'Comment le système pourrait-il être utilisé pour discriminer certains groupes de candidats?'"...  
  * **Ethical Prompting \+ Explainable Prompting:**  Utilisez Explainable Prompting pour comprendre le raisonnement éthique du modèle.  
  * **Counterfactual Prompting \+ Explainable Prompting:**  Utilisez Explainable Prompting pour comprendre pourquoi le modèle a généré un scénario contrefactuel spécifique.  
    * Exemple:"Un modèle a généré une solution qui semble discriminatoire. Utilisez Explainable Prompting pour demander au modèle de justifier sa solution et d'expliquer les facteurs qui ont influencé sa décision."...

## 6\. Simulation Prédictive  (Monte Carlo, Digital Twin, Régression Prédictive )

* **Description:** Testez la robustesse des solutions à l'aide de simulations statistiques (Monte Carlo) et de scénarios extrêmes.

* **Méthodes:**

  * **Monte Carlo:** Estimez des valeurs et des probabilités en simulant un grand nombre d'événements aléatoires.

* **Échantillonnage aléatoire:** Générez des nombres aléatoires pour simuler des variables.

* **Loi des grands nombres:** La moyenne des résultats converge vers la valeur attendue lorsque le nombre de simulations augmente.

* **Intervalle de confiance:** Estimez la précision des résultats.

* **Formules:**

  * **Générateur de nombres aléatoires:** `x(i+1) = (a * x(i) + c) mod m` (où `a`, `c`, et `m` sont des constantes)  
  * **Moyenne:** `μ = (1/n) * Σ xi`  
  * **Écart-type:** `σ = √((1/(n-1)) * Σ (xi - μ)²)`  
  * **Intervalle de confiance:** `μ ± z * (σ / √n)` (où `z` est la valeur critique de la distribution normale)

* **Types de méthodes Monte Carlo:**

  * **Importance Sampling:** Concentrez l'échantillonnage sur les valeurs les plus importantes.  
  * **Markov Chain Monte Carlo (MCMC):** Générez des échantillons à partir d'une distribution de probabilité complexe.

* **Digital Twin:** Créez un modèle virtuel d'un système pour simuler son comportement et prédire ses performances. \\

* **Modèle mathématique:** Utilisez des équations et des algorithmes pour représenter le système.

* **Données en temps réel:** Intégrez des données provenant de capteurs et d'autres sources.

* **Simulation:** Exécutez le modèle pour prédire le comportement du système dans différentes conditions.

* **Intégration TDA Avancée :**

  * \- Analyse de persistance homologique pour modéliser les structures multi-échelles

  * \- Filtrage de Mapper pour la visualisation de datasets complexes

  * \- Classes de caractéristiques topologiques : Betti numbers, persistence diagrams

* **Types de Digital Twins:**

  * **Produit:** Simulez le comportement d'un produit tout au long de son cycle de vie.

  * **Processus:** Optimisez les processus de fabrication et de production.

  * **Système:** Surveillez et gérez des systèmes complexes, tels que des bâtiments ou des réseaux électriques.

      \*\*Agent-Based Modeling (ABM):\*\* Simulez le comportement d'agents individuels et leurs interactions pour comprendre les dynamiques du système. \\

* **Agents:** Entités autonomes avec des comportements et des règles de décision.

* **Environnement:** Espace où les agents interagissent.

* **Interactions:** Règles définissant comment les agents interagissent entre eux et avec l'environnement.

* ### **Régression Prédictive (Linéaire/Logistique)**

  Utilisez des modèles de régression pour prédire des résultats continus (régression linéaire) ou classifier des catégories (régression logistique) en fonction de variables explicatives.  
  **Méthodes:**

* **Régression Linéaire:**

  * **Formule:** ( y \= \\beta\_0 \+ \\beta\_1x\_1 \+ \\epsilon )  
  * **Cas d'usage:** Prédire un résultat quantitatif (ex : ventes, coûts).  
  * **Prompt Exemple:**  
    *"Analysez le dataset \[lien\] et identifiez les variables influençant les ventes. Utilisez une régression linéaire pour prédire les ventes mensuelles en fonction du budget marketing et des prix. Présentez les coefficients et l’intervalle de confiance."*

* **Régression Logistique:**

  * **Formule:** ( P(y=1) \= \\frac{1}{1 \+ e^{-(\\beta\_0 \+ \\beta\_1x\_1)}} )  
  * **Cas d'usage:** Classifier des résultats binaires (ex : succès/échec).  
  * **Prompt Exemple:**  
    *"À partir du dataset \[lien\], déterminez la probabilité qu'un client souscrive à un service en fonction de son âge et de son historique. Utilisez une régression logistique et interprétez les odds ratios."*

* ### **Régression Régularisée (Ridge/Lasso)**

Utilisez des modèles de régression avec régularisation L2 (Ridge) ou L1 (Lasso) pour réduire le surajustement (*overfitting*) et améliorer la généralisation.

**Méthodes :**

* **Régression Ridge (L2) :**

  * **Formule :**  
    \[ \\text{Coût} \= \\sum\_{i=1}^n (y\_i \- \\hat{y}*i)^2 \+ \\lambda \\sum*{j=1}^p \\beta\_j^2 \]  
  * **Cas d'usage :**  
    Prédiction avec variables corrélées (ex : données multicollinéaires).  
  * **Prompt Exemple :**  
    *"Analysez le dataset \[lien\] avec 50 variables corrélées. Appliquez une régression Ridge pour prédire les ventes. Optimisez (\\lambda) par validation croisée et interprétez la stabilité des coefficients."*

* **Régression Lasso (L1) :**

  * **Formule :**  
    \[ \\text{Coût} \= \\sum\_{i=1}^n (y\_i \- \\hat{y}*i)^2 \+ \\lambda \\sum*{j=1}^p |\\beta\_j| \]  
  * **Cas d'usage :**  
    Sélection de variables (réduction de dimension) pour des modèles parcimonieux.  
  * **Prompt Exemple :**  
    *"Identifiez les 5 variables les plus influentes sur la rétention client en utilisant Lasso. Visualisez le chemin de régularisation et expliquez les variables éliminées."*

  ### 

* ### **Régression Polynomiale**

  Modélisez des relations non linéaires en étendant la régression linéaire avec des termes polynomiaux. Idéal pour capturer des courbes complexes dans les données.  
  **Méthode :**  
* **Formule Mathématique :**  
  \[ y \= \\beta\_0 \+ \\beta\_1x \+ \\beta\_2x^2 \+ \\dots \+ \\beta\_nx^n \+ \\epsilon \]  
  * **Fonction de Coût (MSE) :**  
    \[ \\text{MSE} \= \\frac{1}{N} \\sum\_{i=1}^N \\left( y\_i \- (\\beta\_0 \+ \\beta\_1x\_i \+ \\dots \+ \\beta\_nx\_i^n) \\right)^2 \]  
  * **Cas d'usage :**  
    Prédire des tendances non linéaires (ex : croissance exponentielle, courbes de saturation).  
  * **Degré Polynomial :**  
    Choix critique pour éviter le surajustement (*overfitting*).

![][image2]

**Prompt Exemple :**  
*"Analysez le dataset \[lien\] des ventes sur 5 ans. Utilisez une régression polynomiale (degré 3\) pour modéliser la croissance non linéaire. Visualisez la courbe et évaluez le R² ajusté."*

**Exemple de simulation avec softmax :**  
*"Simulez un scénario où 30% des données sont corrompues par du bruit. Utilisez une régression logistique multinomiale pour prédire les classes et mesurez l’impact sur l’accuracy via **Monte Carlo**. Visualisez la matrice de confusion pour identifier les classes les plus sensibles."*

**Simulation de robustesse SVM :**  
*"Testez la robustesse d’un SVM à des attaques adversariales. Injectez du bruit dans 20% des données et mesurez la dégradation de l’accuracy avec **Monte Carlo**. Comparez les performances avec/without normalisation des caractéristiques."*

**Exemple de simulation Monte Carlo pour RNA :**  
*"Injectez du bruit gaussien ((\\mu=0, \\sigma=0.2)) dans les données d’entraînement d’un RNA. Mesurez la dégradation de l’accuracy via 1000 itérations Monte Carlo. Comparez avec un modèle linéaire pour évaluer la résilience aux perturbations."*

* ### **Régression Quantile**

  Estimez des quantiles spécifiques (ex: médiane, 90e percentile) de la distribution conditionnelle de la variable cible, plutôt que la moyenne. Idéal pour analyser l’impact des variables sur différentes parties de la distribution (ex: queues de distribution).

**Méthode :**

- **Fonction de Perte (Pinball Loss) :**  
  \[ \\mathcal{L}\_\\tau(y, \\hat{y}) \= \\begin{cases} \\tau \\cdot |y \- \\hat{y}| & \\text{si } y \\geq \\hat{y} \\ (1 \- \\tau) \\cdot |y \- \\hat{y}| & \\text{si } y \< \\hat{y} \\end{cases} \]  
  - ( \\tau \\in \[0, 1\] ): Quantile cible (ex: ( \\tau \= 0.5 ) pour la médiane).  
- **Formulation d’Optimisation :**  
  \[ \\min\_{\\boldsymbol{\\beta}} \\sum\_{i=1}^N \\mathcal{L}\_\\tau(y\_i, \\boldsymbol{\\beta} \\cdot \\mathbf{x}\_i) \]

![][image3]

**Cas d’usage :**

- Prédiction d’intervalles de confiance (ex: prix immobiliers, demande énergétique).  
- Analyse de risques (ex: VaR en finance).  
- Données hétéroscédastiques ou avec outliers.

**Prompt Exemple :**  
*"Analysez l’impact du revenu sur les dépenses de santé pour le 10e et 90e percentile. Utilisez une régression quantile ((\\tau=0.1) et (\\tau=0.9)) pour identifier les disparités socio-économiques. Visualisez les intervalles avec des bandes de confiance."*

* **Synergies et Combos Possibles:**

  * **Intégrez avec Monte Carlo** pour simuler des distributions de queues épaisses.  
  * **Combinez avec Digital Twin** pour modéliser des scénarios extrêmes (ex: crise économique).  
  * **Validez avec Self-Consistency** (Page 21\) pour vérifier la cohérence des quantiles estimés.  
  * **Intégrez avec Monte Carlo** pour tester la stabilité du modèle sous différents degrés polynomiaux.  
  * **Combinez avec Digital Twin** pour simuler des scénarios basés sur des extrapolations polynomiales.  
  * **Validez avec Cross-Modal Prompting** (Page 21\) pour comparer les prédictions avec des graphiques ou équations alternatives.  
  * **Intégrez avec Monte Carlo** pour évaluer la robustesse des coefficients sous différentes valeurs de (\\lambda).  
  * **Combinez avec Digital Twin** pour simuler l’impact de variables sélectionnées par Lasso sur un système virtuel.  
  * **Validez avec Self-Consistency** (Page 21\) pour vérifier la cohérence des variables retenues.  
  * **Intégrez la régression avec Monte Carlo** pour évaluer l'incertitude des prédictions.  
  * **Combinez avec Digital Twin** pour simuler des scénarios basés sur les prédictions du modèle.  
  * **Validez les résultats avec Self-Consistency** (Page 21\) pour vérifier la cohérence des coefficients.  
  * **Monte Carlo \+ Digital Twin:** Utilisez Monte Carlo pour simuler l'incertitude dans les paramètres du Digital Twin et évaluer la robustesse des prédictions.  
  * **Monte Carlo \+ ABM:** Simulez des scénarios avec des agents hétérogènes et des comportements stochastiques.  
  * **Digital Twin \+ ABM:** Intégrez des agents dans le Digital Twin pour simuler des interactions complexes et des comportements émergents.  
  * **Chain-of-Thought \+ Simulation:** Utilisez Chain-of-Thought pour décomposer le problème de simulation en étapes et guider le modèle dans le choix des méthodes et des paramètres.  
  * **Knowledge-Enriched Prompting \+ Simulation:** Intégrez des connaissances externes dans les prompts pour améliorer la précision et la pertinence des simulations.

Exemples:

* **Prédire la propagation d'une épidémie (ABM):** Simulez le comportement des individus et leurs interactions pour prédire la propagation d'une maladie.  
* **Optimiser la gestion d'un réseau de transport (Digital Twin \+ Monte Carlo):** Créez un Digital Twin du réseau et utilisez Monte Carlo pour simuler différents scénarios de trafic et optimiser les flux.  
* **Évaluer l'impact d'une nouvelle politique (ABM \+ Monte Carlo):** Simulez le comportement des agents économiques et sociaux en réponse à une nouvelle politique et utilisez Monte Carlo pour évaluer l'incertitude des résultats. \*

## 7\. Prototypage Adaptatif (ReAct, Greedy Search)

* **Description:** Créez un prototype minimal viable (MVP) pour la solution la plus prometteuse. Décrivez les étapes nécessaires pour le développer.

* **Méthodes:**

  * **ReAct (Reason \+ Act):** Alternez le raisonnement et l'action pour développer le prototype de manière itérative.  
    * Boucle ReAct: (Raisonner, Agir, Observer,... )  
    * Fonction de récompense: R(s, a)  
    * Description:  Combine le raisonnement et l'action pour un développement itératif.

* **Greedy Search:** Sélectionnez les actions qui semblent les plus prometteuses à chaque étape, sans planification à long terme. \\

* **Fonction d'évaluation:** h(n) (où n est un nœud) \- permet d'estimer la valeur d'un état ou d'une action.

* **Choix glouton:** Sélectionner le nœud qui maximise la fonction d'évaluation à chaque étape.

* **Description:** Utile pour explorer rapidement un espace de solutions, mais peut ne pas trouver la solution optimale globale.

* **Hill Climbing:** Une variante de Greedy Search qui explore l'espace des solutions en se déplaçant itérativement vers le voisin le plus prometteur.

* **Voisinage:** Ensemble des solutions voisines de la solution actuelle.

* **Déplacement:** Passer d'une solution à une solution voisine.

* **Description:** Peut se bloquer dans des optima locaux.

* **Simulated Annealing:** Une métaheuristique inspirée du processus de recuit en métallurgie, qui permet d'échapper aux optima locaux en acceptant parfois des solutions moins bonnes.

* **Température:** Paramètre contrôlant la probabilité d'accepter des solutions moins bonnes.

* **Refroidissement:** Diminution progressive de la température au cours de la recherche.

* **Description:** Explore l'espace des solutions de manière plus globale que Hill Climbing.

* **Synergies et Combos Possibles:**

  * **ReAct \+ Chain-of-Thought (CoT):** Guider le raisonnement pour planifier les actions et évaluer les résultats.  
  * **Greedy Search \+ Tree-of-Thoughts (ToT):** Explorer l'arbre des solutions en choisissant les branches les plus prometteuses à chaque étape.  
  * **Hill Climbing \+ Recherche Locale:** Combiner Hill Climbing avec des techniques de recherche locale pour améliorer l'exploration du voisinage.  
  * **Simulated Annealing \+ Algorithmes Génétiques:** Utiliser Simulated Annealing pour améliorer la recherche des algorithmes génétiques en explorant l'espace des solutions de manière plus globale.  
  * **ReAct \+ Feedback-Driven Prompting:** Ajuster les actions et le raisonnement en fonction du feedback.

Exemples:

* **Développement d'un chatbot (ReAct):** Alterner entre la génération de réponses et l'évaluation de la satisfaction de l'utilisateur pour améliorer le chatbot de manière itérative.  
* **Optimisation des paramètres d'un modèle (Hill Climbing):** Explorer l'espace des paramètres en ajustant les valeurs et en évaluant les performances du modèle à chaque étape.  
* **Conception d'un produit (Simulated Annealing):** Explorer l'espace des designs possibles en acceptant parfois des designs moins bons pour éviter de se bloquer dans des optima locaux. \*

## 8\. Déploiement Contextuel  \\

(Voronoi Tessellation, Dynamic Prompting)

* **Description:** Adaptez la solution au contexte local en tenant compte des spécificités géographiques, culturelles et économiques.

* **Méthodes:**

  * **Voronoi Tessellation:** Divisez l'espace en zones d'influence pour adapter la solution aux différentes régions.  
    * Distance euclidienne: d(p, q) \= √((p1 \- q1)² \+ (p2 \- q2)²)  
    * Diagramme de Voronoi: Partitionnement du plan en régions.  
    * Description:  Créez des zones d'influence pour un déploiement adapté.  
  * **\*\*Dynamic Prompting: \*\***Ajustez les prompts en fonction du contexte local.  
    * Fonction de contexte: c(x)  
    * Prompt dynamique: p(x) \= p0 \+ f(c(x))  
    * Description:  Adaptez les prompts en fonction du contexte pour une meilleure pertinence.  
  * **K-Nearest Neighbors (KNN):** Classifier un point en fonction des k points les plus proches dans l'espace des caractéristiques. \\

* **Distance:** Mesure de la similarité entre les points (ex: distance euclidienne, distance de Manhattan).

* **Voisinage:** Ensemble des k points les plus proches.

* **Classification:** Attribuer au point la classe majoritaire parmi ses k voisins.

* **Description:** Utile pour adapter la solution aux contextes locaux en fonction de la similarité avec des contextes connus.

* **Geographically Weighted Regression (GWR):** Une méthode statistique qui permet de modéliser les relations spatiales entre les variables. \\

  * **Pondération spatiale:** Attribuer des poids aux observations en fonction de leur distance par rapport au point d'intérêt.  
  * **Régression locale:** Estimer les paramètres du modèle pour chaque point d'intérêt en utilisant les observations pondérées.  
  * **Description:** Utile pour modéliser des phénomènes spatiaux et adapter la solution aux variations géographiques.

* **Synergies et Combos Possibles:**

  * **Voronoi Tessellation \+ Knowledge-Enriched Prompting:** Fournissez des informations sur les régions.  
  * **Dynamic Prompting \+ Chain-of-Thought (CoT):** Guidez le raisonnement en adaptant les prompts à chaque étape.  
  * **KNN \+ Apprentissage Automatique:** Utilisez KNN pour classifier les contextes et adapter la solution en fonction des classes.  
  * **GWR \+ Analyse Spatiale:** Utilisez GWR pour modéliser les relations spatiales et adapter la solution aux variations géographiques.

Exemples:

* **Adaptation d'un service de livraison (Voronoi Tessellation):** Divisez la ville en zones de livraison et adaptez les horaires et les itinéraires en fonction des caractéristiques de chaque zone.  
* **Personnalisation d'un chatbot (Dynamic Prompting):** Ajustez les réponses du chatbot en fonction du profil de l'utilisateur et de l'historique de la conversation.  
* **Recommandation de produits (KNN):** Recommandez des produits aux utilisateurs en fonction des préférences de leurs voisins dans l'espace des caractéristiques.  
* **Prédiction de la demande (GWR):** Prédisez la demande pour un produit ou un service en fonction des variations géographiques et des facteurs locaux. \*

## 9\. Suivi Dynamique (IoT Integration, Process Mining)

* **Description:** Proposez un système de monitorage en temps réel pour suivre l'impact de la solution et détecter d'éventuels problèmes.

* **Méthodes:**  
  * **IoT Integration:** Collectez des données en temps réel à partir de capteurs et d'appareils connectés.  
    * Données temporelles: x(t)  
    * Analyse de séries temporelles: Moyenne mobile, Lissage exponentiel, Analyse de Fourier.  
    * Description:  Surveillez le système en temps réel avec des données provenant de capteurs.  
  * **Process Mining:** Analysez les données pour identifier les goulots d'étranglement et les inefficacités.  
    * Log d'événements, Modèle de processus, Indicateurs de performance.  
    * Description:  Analysez les données du processus pour identifier les points à améliorer.

\\

**\* \*\*Contrôle de Processus Statistique (SPC):\*\*** Utilisez des outils statistiques pour surveiller et contrôler un processus. \\

* **Cartes de contrôle:** Visualisez les variations du processus et détectez les anomalies.  
* **Indicateurs de capabilité:** Mesurez la capacité du processus à respecter les spécifications.  
* **Description:** Permet de maintenir la stabilité et la performance du processus.  
* **Machine Learning pour la surveillance:** Utilisez des algorithmes d'apprentissage automatique pour analyser les données et détecter les anomalies.  
* **Apprentissage supervisé:** Entraînez un modèle sur des données étiquetées pour classifier les événements normaux et anormaux.  
* **Apprentissage non supervisé:** Identifiez les anomalies en détectant les schémas inhabituels dans les données.  
* **Description:** Permet d'automatiser la détection des anomalies et d'améliorer la précision. \*

* **Synergies et Combos Possibles:**  
  * **IoT Integration \+ Dynamic Prompting:** Adaptez les prompts en fonction des données en temps réel.  
  * **Process Mining \+ Chain-of-Thought (CoT):** Analysez les données du processus et identifiez les points à améliorer.  
  * **SPC \+ Automatisation:** Intégrez les cartes de contrôle dans un système d'automatisation pour ajuster le processus en temps réel.  
  * **Machine Learning \+ IoT:** Analysez les données des capteurs avec des algorithmes de Machine Learning pour prédire les pannes et optimiser la maintenance.

Exemples:

* **Surveillance d'une chaîne de production (IoT \+ Machine Learning):** Collectez des données sur les machines en temps réel et utilisez des algorithmes de Machine Learning pour détecter les anomalies et prédire les pannes.  
* **Optimisation d'un processus logistique (Process Mining \+ SPC):** Analysez les données du processus logistique et utilisez des cartes de contrôle pour identifier les goulots d'étranglement et améliorer l'efficacité.  
* **Surveillance de la qualité de l'air (IoT \+ Dynamic Prompting):** Collectez des données sur la qualité de l'air en temps réel et adaptez les messages d'alerte en fonction du niveau de pollution. \*

## 10\. Boucle d'Amélioration Continue  \\

(Kaizen, Feedback-Driven Prompting)

* **Description:** Implémentez une boucle Kaizen pour ajuster progressivement la solution en fonction des retours d'expérience.

* **Méthodes:**

  * **Amélioration itérative des quantiles :**  
    *"Après le déploiement d’un modèle de prévision de demande, utilisez la régression quantile ((\\tau=0.05, 0.5, 0.95)) pour ajuster les intervalles de confiance mensuels. Intégrez les feedbacks des utilisateurs via **Feedback-Driven Prompting** pour affiner les paramètres (\\tau) en fonction des erreurs passées."*

  * **Amélioration itérative des modèles :**  
    *"Après le déploiement initial, utilisez Lasso pour identifier les variables redondantes. Ajustez itérativement (\\lambda) via **Feedback-Driven Prompting** pour optimiser le modèle en fonction des nouvelles données."*

  * **\*\*Méthode Kaizen: \*\***Amélioration continue par petits ajustements.

    * Cycle PDCA: (Planifier, Faire, Vérifier, Agir)  
    * Amélioration incrémentale: y(t+1) \= y(t) \+ Δy(t)  
    * Description:  Améliorez le système par itérations successives.

  * **\*\*Feedback-Driven Prompting: \*\***Utilisez les commentaires des utilisateurs pour améliorer les prompts et la solution.

    * Fonction de feedback: F(p, r)  
    * Adaptation du prompt: p(t+1) \= p(t) \+ α \* F(p(t), r(t))  
    * Description:  Ajustez les prompts en fonction du feedback pour améliorer les réponses.

  * **\*\*Refinement Prompting: \*\*** Demandez au modèle d'améliorer une solution existante en fonction de critères spécifiques ou de nouvelles informations.

    * Description:  Utile pour affiner les solutions et les rendre plus précises, complètes et adaptées au contexte. \\

  * **Socratic Prompting:**  Posez une série de questions pour guider le modèle vers une meilleure compréhension du problème et la découverte de solutions.

    * Description:  Utile pour encourager la réflexion critique, l'auto-évaluation et l'apprentissage.

* ### **Rétropropagation (Backpropagation)**

  Optimisez les poids du RNA via la descente de gradient stochastique (SGD) et la règle de la chaîne.  
  **Formules :**

* **Gradient de la perte par rapport aux poids :**  
  \[ \\frac{\\partial \\mathcal{L}}{\\partial W^{(l)}} \= \\delta^{(l)} \\cdot a^{(l-1)^\\top} \]

  * ( \\delta^{(l)} \= \\frac{\\partial \\mathcal{L}}{\\partial z^{(l)}} ): Gradient de l’erreur à la couche ( l ).

* **Mise à jour des poids :**  
  \[ W^{(l)} \\leftarrow W^{(l)} \- \\eta \\frac{\\partial \\mathcal{L}}{\\partial W^{(l)}} \]

  * ( \\eta ): Taux d’apprentissage (*learning rate*).

  **Prompt Exemple :**  
    *"Analysez la convergence d’un RNA avec SGD ((\\eta=0.01)) et Momentum ((\\beta=0.9)). Tracez la courbe de perte et identifiez les oscillations ou les plateaux."*

* **Synergies et Combos Possibles:**

  * **Kaizen \+ Iterative Prompting:**  Améliorez les prompts de manière itérative.

    1. **Exemple d'application Kaizen avec Régression:**  
       *"Après le déploiement d'une solution, collectez des données sur son efficacité. Utilisez une régression linéaire pour identifier les facteurs clés d'amélioration. Affinez itérativement le modèle avec de nouveaux données via Feedback-Driven Prompting."*

  * **Feedback-Driven Prompting \+ Active Prompting:**  Sélectionnez les prompts les plus prometteurs....

  * **Intégrez l'analyse des causes profondes (Root-Cause Analysis)** pour identifier les causes des problèmes et les résoudre efficacement.

  * **Kaizen \+ Refinement Prompting:  Utilisez Refinement Prompting** **à chaque itération du cycle PDCA** pour améliorer progressivement la solution.

  * **Feedback-Driven Prompting \+ Refinement Prompting**:  Utilisez le feedback pour identifier les points faibles de la solution et demandez au modèle de les améliorer avec Refinement Prompting.

    * Exemple:"Un modèle a généré un texte avec des erreurs grammaticales. Utilisez Refinement Prompting pour demander au modèle de corriger les erreurs et d'améliorer la clarté du texte."...

  * **Kaizen \+ Socratic Prompting:**  Utilisez Socratic Prompting à chaque itération du cycle PDCA pour identifier les points à améliorer.

  * **Feedback-Driven Prompting \+ Socratic Prompting:**  Utilisez Socratic Prompting pour obtenir un feedback plus précis et constructif du modèle.

Exemple:"Un modèle a généré une solution qui ne répond pas complètement aux exigences du problème. Utilisez Socratic Prompting pour poser des questions comme: 'Quelles sont les limites de cette solution?' ou 'Comment pourrait-on améliorer cette solution pour qu'elle réponde mieux aux contraintes?'."...

\* 

## 11\. Capitalisation Cognitive  \\

(CoK, Knowledge-Enriched Prompting)

* **Description:** Archivez les apprentissages clés dans une base de connaissances structurée pour faciliter leur réutilisation future.

* **Méthodes:**  
  * **Nouvelle architecture hybride :**  
    * \- Graph Attention Networks (GAT) pour l'analyse des graphes de connaissances  
    * \- Message Passing Neural Networks (MPNN) pour le raisonnement relationnel  
    * \- Layer-wise propagation rule : \\(H^{(l+1)} \= \\sigma(\\tilde{D}^{-\\frac{1}{2}}\\tilde{A}\\tilde{D}^{-\\frac{1}{2}}H^{(l)}W^{(l)})\\)

  * **Chain-of-Knowledge (CoK):** Créez une chaîne de connaissances pour relier les informations et les apprentissages.  
    * Graphe de connaissances: (Nœuds, Arêtes)  
    * Métriques de similarité: Similarité cosinus, Distance euclidienne.  
    * Description:  Organisez les connaissances pour faciliter la recherche et l'inférence.  
  * **Knowledge-Enriched Prompting:** Enrichissez les prompts avec des informations provenant de la base de connaissances.  
    * Représentation vectorielle des connaissances: Vecteurs de mots, plongements.  
    * Intégration des connaissances dans le prompt: Concaténation, attention.  
    * Description:  Intégrez les connaissances dans le prompt pour améliorer les réponses.

* **Synergies et Combos Possibles:**  
  * **CoK \+ RAG:**  Récupérez des informations pertinentes et intégrez-les dans le prompt.  
  * **Knowledge-Enriched Prompting \+ CoT:**  Guidez le raisonnement en fournissant des connaissances contextuelles....  
  * **Intégrez l'apprentissage fédéré** pour mettre à jour la base de connaissances de manière décentralisée.

## 12\. Validation Transversale  \\

(Self-Consistency, Ensemble Prompting)

* **Description:** Croisez les résultats avec différentes approches (Self-Consistency, Ensemble Prompting) pour garantir leur robustesse.  
* **Méthodes:**

  * **Validation des intervalles quantiles :**  
    *"Comparez les intervalles prédits par régression quantile avec ceux d’une forêt aléatoire quantile. Utilisez **Ensemble Prompting** pour agréger les résultats et **Cross-Modal Prompting** pour visualiser les écarts via des graphiques en violon."*

  * **Validation multiclasse :**  
    *"Comparez les performances d’un modèle multinomial avec une forêt aléatoire en utilisant **Ensemble Prompting**. Agrégez les résultats via un vote majoritaire pondéré et vérifiez la cohérence avec **Self-Consistency**."*

  * **\*\*Self-Consistency: \*\***Vérifiez la cohérence des résultats entre différentes générations.

    * Cohérence: Mesure de la similarité entre les réponses.  
    * Métriques de similarité: Similarité cosinus, Distance euclidienne, BLEU.  
    * Description:  Évaluez la cohérence des réponses en comparant les résultats de multiples générations.

  * **Ensemble Prompting:** Combinez plusieurs prompts pour obtenir une réponse plus robuste.

    * Pondération des réponses: w1, w2,..., wn  
    * Combinaison des réponses: r \= Σ wi \* ri  
    * Description:  Combinez les réponses de plusieurs modèles pour une meilleure robustesse. \\

  * **Cross-Modal Prompting:** Utilisez des informations provenant de différentes modalités (texte, image, son) pour enrichir le prompt et obtenir des réponses plus complètes.

    * Description:  Permet de combiner les forces de différentes modalités et d'exploiter les relations entre elles.

  * **Exemple de validation polynomiale :**  
    *"Comparez un modèle polynomial (degré 4\) et un modèle linéaire sur le même dataset. Utilisez **Self-Consistency** pour vérifier si les prédictions polynomiales restent cohérentes après augmentation du bruit dans les données. Appliquez une validation croisée en (k)-folds pour optimiser le degré polynomial."*

  * **Exemple de validation SVM :**  
    *"Comparez un SVM avec noyau RBF et une forêt aléatoire sur un dataset de diagnostic médical. Utilisez **Ensemble Prompting** pour générer des prédictions hybrides et **Self-Consistency** pour vérifier la cohérence des résultats. Visualisez les frontières de décision avec des graphiques 2D."*  
  * 

* **Synergies et Combos Possibles:**  
  * **Self-Consistency \+ Optimisation bayésienne:**  Maximisez la cohérence des réponses.  
  * **Ensemble Prompting \+ CoT:**  Combinez les réponses de plusieurs modèles qui utilisent CoT....  
  * **Intégrez Cross-Modal Prompting** pour valider les résultats en utilisant différentes modalités (texte, image, etc.).  
  * **Self-Consistency \+ Cross-Modal Prompting:**  Utilisez Cross-Modal Prompting pour générer des réponses plus riches et variées, puis utilisez Self-Consistency pour vérifier leur cohérence.  
  * **Ensemble Prompting \+ Cross-Modal Prompting:**  Combinez les réponses de plusieurs modèles qui utilisent Cross-Modal Prompting pour obtenir une vision plus complète du problème.  
    * Exemple:"Analyser une image et générer une description textuelle. Utilisez Cross-Modal Prompting en fournissant à la fois l'image et des informations textuelles sur le contexte de l'image."...

## 13\. Communication Stratégique  \\

(Role-Playing Prompting, Contrastive Prompting)

* **Description:** Présentez les résultats sous forme d'un rapport clair et adapté aux parties prenantes, avec des recommandations concrètes.

* **Méthodes:**  
  * **Role-Playing Prompting:** Adaptez la communication au public cible en jouant différents rôles.  
    * Persona: P \= (Attributs, Objectifs, Relations)  
    * Adaptation du langage: L(P)  
    * Description:  Adaptez la communication en fonction du public cible.  
  * **Contrastive Prompting:** Mettez en évidence les différences entre les solutions pour faciliter la prise de décision.  
    * Comparaison: S(A, B)  
    * Contraste: C(A, B) \= 1 \- S(A, B)  
    * Description:  Mettez en évidence les différences entre les solutions.

  * **Exemple de validation pour Ridge/Lasso :**  
    *"Comparez les performances de Ridge et Lasso sur le même dataset en utilisant **Ensemble Prompting**. Générez des prompts distincts pour chaque méthode, agrégez les résultats via une moyenne pondérée, et validez la cohérence des variables sélectionnées par Lasso avec une analyse manuelle."*

* **Synergies et Combos Possibles:**  
  * **Role-Playing Prompting \+ Knowledge-Enriched Prompting:**  Fournissez des informations sur le public cible.  
  * **Contrastive Prompting \+ CoT:**  Comparez et contrastez les solutions étape par étape....  
  * **Utilisez Role-Playing Prompting avec la PNL** (Programmation Neuro-Linguistique) pour adapter le langage et le style de communication.  
  * Intégrez des graphiques et des visualisations pour rendre les résultats plus clairs et plus compréhensibles.

## 14\. Extension Évolutive  \\

(Transfer Learning Prompting, Adaptive Prompting)

* **Description:** Suggérez comment étendre la solution à d'autres contextes similaires tout en tenant compte des spécificités locales.

* **Méthodes:**

  * **Transfer Learning avec RNA :**  
    *"Adaptez un modèle pré-entraîné (ex: ResNet) à un nouveau contexte en utilisant **Transfer Learning Prompting**. Geler les couches basses et réentraînez les couches supérieures sur un dataset réduit (ex: 1000 images). Mesurez le gain en temps et en performance."*

  * **Transfer Learning Prompting:** Transférez les connaissances et les apprentissages à de nouveaux problèmes et domaines.  
    * Similitude des tâches: S(T1, T2)  
    * Transfert des connaissances: K(T1) \-\> K(T2)  
    * Description:  Réutilisez les connaissances acquises sur une tâche pour résoudre une nouvelle tâche.  
    * \*\*Intégration avancée\*\* :    
    * \- Matrice de transfert \\( \\Phi: \\mathcal{T}\_\\text{source} \\rightarrow \\mathcal{T}\_\\text{cible} \\)    
    * \- Mécanisme d'attention croisée pour l'alignement contextuel    
    * \- Fine-tuning adaptatif avec régularisation L2 contextuelle

  * **Adaptive Prompting:** Adaptez les prompts et la solution aux nouveaux contextes.  
    * Adaptation des paramètres: θ(t+1) \= θ(t) \+ α \* Δθ(t)  
    * Métriques de performance: Précision, Rappel, F1-score.  
    * Description:  Ajustez les prompts en fonction des performances.

* **Synergies et Combos Possibles:**  
  * **Transfer Learning Prompting \+ Knowledge-Enriched Prompting:**  Enrichissez les prompts en transférant des connaissances.  
  * **Adaptive Prompting \+ Feedback-Driven Prompting:**  Utilisez le feedback pour ajuster les prompts....  
  * **Utilisez Transfer Learning** avec l'apprentissage automatique pour automatiser l'adaptation de la solution.  
  * **Intégrez l'apprentissage fédéré** pour partager les connaissances et les apprentissages entre différents contextes tout en préservant la confidentialité.

## 

## 

## 

## « Techniques avancées »

## Instruction Tuning:

Description: Fine-tune the language model on a dataset of instructions and desired outputs to improve its ability to understand and follow instructions.

Benefits:

* Improved accuracy and relevance of responses.  
* Better generalization to new instructions.  
* Enhanced ability to perform complex tasks.

Example:

Fine-tune the model on a dataset of instructions for writing different types of creative content (poems, code, scripts, musical pieces, email, letters, etc.) to improve its ability to generate diverse content formats....

**Justification:**

Le réglage des instructions est une technique avancée qui peut améliorer considérablement les capacités des modèles de langage. En l'incluant dans votre fichier « Instructions-Prompts », vous fournissez aux utilisateurs un outil puissant pour personnaliser et optimiser les performances du modèle en fonction de leurs besoins spécifiques.

**Considérations supplémentaires :**

* **Exigences en matière de données :** le réglage des instructions nécessite un ensemble de données substantiel d’instructions et de sorties souhaitées.  
* **Ressources informatiques :** le réglage précis d’un modèle de langage peut être coûteux en termes de calcul.  
* **Expertise :** Le réglage des instructions nécessite une expertise en apprentissage automatique et en traitement du langage naturel.

En intégrant Instruction Tuning dans votre guide, vous permettez aux utilisateurs d'exploiter cette technique avancée et de libérer tout le potentiel des modèles linguistiques pour résoudre des problèmes complexes et générer du contenu créatif.

## Meta-Learning Prompting:

Description: Train the language model on a variety of tasks and prompts to improve its ability to adapt to new, unseen tasks.

Benefits:

* Enhanced generalization to new tasks.  
* Improved learning efficiency from fewer examples.  
* Increased flexibility and adaptability.

Example:Train the model on a diverse set of tasks, such as translation, summarization, question answering, and code generation, to improve its ability to learn new tasks more efficiently....

## Ingénierie des Invites... (autres techniques existantes)...

### **In-Context Learning:**

Description: Fournissez au modèle des exemples de paires entrée-sortie pour lui montrer comment effectuer la tâche.

Exemple:

Entrée: Traduisez les phrases suivantes en français:

* Hello, how are you?  
* I am fine, thank you.

Sortie:

* Bonjour, comment allez-vous?  
* Je vais bien, merci.

Entrée: Traduisez la phrase suivante en français: The weather is nice today.

Sortie: Le temps est beau aujourd'hui....

### **Personalized Prompting:**

Description: Tailor prompts to individual users based on their preferences, knowledge, or goals.

Benefits:

* Increased user engagement.  
* More relevant and personalized responses.  
* Improved user satisfaction.

Example:

For a user interested in history, include historical references in the prompt. For a user interested in sports, use sports-related analogies or examples....

### **Collaborative Prompting:**

Description: Involve multiple agents, such as humans and language models, in an iterative process to create and refine prompts.

Benefits:

* Leverages human creativity and domain expertise.  
* Utilizes AI's ability to process information and generate diverse outputs.  
* Improves prompt quality and results.

Example:

A human provides an initial prompt, the language model generates variations, and the human selects the best option or provides feedback for further refinement....

### **Context-Aware Prompting:**

Description: Adapt prompts to the specific context of the conversation or the user's needs.

Benefits:

* More relevant and coherent responses.  
* Improved user experience.  
* Enhanced ability to handle complex conversations.

Example:

In a conversation about travel planning, the prompt can be tailored to include information about the user's previous travel experiences or preferences....

[image1]: <data:image/png;base64,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**************************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>

[image2]: <data:image/png;base64,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>

[image3]: <data:image/png;base64,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>