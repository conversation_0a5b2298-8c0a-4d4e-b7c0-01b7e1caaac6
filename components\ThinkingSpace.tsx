import React from 'react';

interface ThinkingSpaceProps {
  log: string[];
  className?: string;
}

const LogIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-sky-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
    </svg>
);


export const ThinkingSpace: React.FC<ThinkingSpaceProps> = ({ log, className = '' }) => {
  return (
    <div className={`${className} p-6 bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 flex flex-col`}>
      <h2 className="text-xl font-semibold mb-4 text-sky-300 flex items-center">
          <LogIcon />
          Espace de Raisonnement
      </h2>
      <div className="flex-grow overflow-y-auto pr-2 -mr-2">
        <ul className="space-y-3">
          {log.length === 0 && <p className="text-slate-400 text-sm">Le journal des actions de l'agent apparaîtra ici...</p>}
          {log.map((entry, index) => (
            <li key={index} className="text-sm text-slate-300 bg-slate-700/50 p-3 rounded-lg">
              <span className="font-mono text-sky-400 mr-2">&gt;</span>{entry}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};