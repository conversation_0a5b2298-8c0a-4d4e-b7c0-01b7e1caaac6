import React, { useState, useRef, useEffect } from 'react';

interface UserInputProps {
  isProcessing: boolean;
  onSendMessage: (message: string) => void;
  isWorkflowStarted: boolean;
  isWorkflowComplete: boolean;
}

export const UserInput: React.FC<UserInputProps> = ({ isProcessing, onSendMessage, isWorkflowStarted, isWorkflowComplete }) => {
  const [text, setText] = useState('');
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim() && !isProcessing) {
      onSendMessage(text);
      setText('');
    }
  };

  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto';
      textAreaRef.current.style.height = `${textAreaRef.current.scrollHeight}px`;
    }
  }, [text]);

  const getButtonText = () => {
    if (isWorkflowComplete) return 'Terminé';
    if (!isWorkflowStarted) return 'Lancer l\'Analyse';
    return 'Envoyer';
  };
  
  const getPlaceholderText = () => {
      if (isWorkflowComplete) return 'Toutes les étapes sont terminées !';
      if (!isWorkflowStarted) return 'Décrivez votre problème pour commencer...';
      return 'Votre réponse...';
  };

  return (
    <form onSubmit={handleSubmit} className="flex items-end gap-3">
      <textarea
        ref={textAreaRef}
        value={text}
        onChange={(e) => setText(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
          }
        }}
        placeholder={getPlaceholderText()}
        className="flex-grow bg-slate-700 rounded-lg p-3 text-slate-200 placeholder-slate-400 resize-none focus:ring-2 focus:ring-indigo-500 focus:outline-none transition-shadow duration-200 max-h-48"
        rows={1}
        disabled={isProcessing || isWorkflowComplete}
      />
      <button
        type="submit"
        disabled={isProcessing || !text.trim() || isWorkflowComplete}
        className="bg-indigo-600 text-white font-semibold py-3 px-5 rounded-lg hover:bg-indigo-500 disabled:bg-slate-600 disabled:cursor-not-allowed transition-colors duration-200 flex-shrink-0"
      >
        {isProcessing ? '...' : getButtonText()}
      </button>
    </form>
  );
};