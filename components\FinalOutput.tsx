import React, { useState } from 'react';

interface FinalOutputProps {
  finalPrompt: string;
}

const CopyIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>
);

export const FinalOutput: React.FC<FinalOutputProps> = ({ finalPrompt }) => {
    const [copyButtonText, setCopyButtonText] = useState('Copier');
    
    const parts = finalPrompt.split('---');
    const optimizedPrompt = parts[0]?.replace('### Le Prompt Optimisé', '').trim() || '';
    const metaAnalysis = parts[1]?.replace('### Méta-Analyse de la Construction', '').trim() || '';

    const handleCopy = () => {
        navigator.clipboard.writeText(optimizedPrompt).then(() => {
            setCopyButtonText('Copié !');
            setTimeout(() => setCopyButtonText('Copier'), 2000);
        });
    };
    
    const handleExport = (format: 'txt' | 'json' | 'md') => {
        let content = '';
        let mimeType = '';
        let filename = `prompt-optimise.${format}`;

        const fullMarkdown = `### Le Prompt Optimisé\n\n${optimizedPrompt}\n\n---\n\n### Méta-Analyse de la Construction\n\n${metaAnalysis}`;

        switch (format) {
            case 'txt':
                content = `Le Prompt Optimisé:\n${optimizedPrompt}\n\n---\n\nMéta-Analyse de la Construction:\n${metaAnalysis}`;
                mimeType = 'text/plain';
                break;
            case 'json':
                content = JSON.stringify({
                    prompt: optimizedPrompt,
                    metaAnalyse: metaAnalysis
                }, null, 2);
                mimeType = 'application/json';
                break;
            case 'md':
                content = fullMarkdown;
                mimeType = 'text/markdown';
                break;
        }

        const blob = new Blob([content], { type: mimeType, endings: 'native' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

  return (
    <div className="flex-grow flex flex-col p-6 overflow-y-auto">
      <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-teal-500 mb-4">
        Résultat Final du Workflow
      </h2>
      
      <div className="bg-slate-900/70 p-4 rounded-xl border border-slate-700 mb-6">
        <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-green-300">Prompt Optimisé</h3>
            <button 
                onClick={handleCopy}
                className="bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm font-semibold py-1 px-3 rounded-md flex items-center transition-colors"
            >
               <CopyIcon /> {copyButtonText}
            </button>
        </div>
        <div className="text-slate-300 whitespace-pre-wrap font-mono text-sm p-3 bg-black/20 rounded-md">
            {optimizedPrompt}
        </div>
      </div>
      
      <div className="bg-slate-900/70 p-4 rounded-xl border border-slate-700">
        <h3 className="text-lg font-semibold text-teal-300 mb-2">Méta-Analyse de la Construction</h3>
        <div className="text-slate-400 prose prose-sm prose-invert max-w-none" dangerouslySetInnerHTML={{ __html: metaAnalysis.replace(/\n/g, '<br />') }} />
      </div>
      
      <div className="mt-6 pt-4 border-t border-slate-700 text-center">
          <h4 className="text-md font-semibold mb-2">Exporter le Prompt</h4>
          <div className="flex gap-2 justify-center">
            <button onClick={() => handleExport('md')} className="bg-sky-600 hover:bg-sky-500 text-white text-sm font-semibold py-1 px-3 rounded-md transition-colors">.md</button>
            <button onClick={() => handleExport('json')} className="bg-sky-600 hover:bg-sky-500 text-white text-sm font-semibold py-1 px-3 rounded-md transition-colors">.json</button>
            <button onClick={() => handleExport('txt')} className="bg-sky-600 hover:bg-sky-500 text-white text-sm font-semibold py-1 px-3 rounded-md transition-colors">.txt</button>
          </div>
      </div>
    </div>
  );
};