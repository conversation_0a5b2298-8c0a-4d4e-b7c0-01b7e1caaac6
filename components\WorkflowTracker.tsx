import React from 'react';
import type { Step } from '../types';

interface WorkflowTrackerProps {
  steps: Step[];
  currentStepIndex: number;
}

export const WorkflowTracker: React.FC<WorkflowTrackerProps> = ({ steps, currentStepIndex }) => {
  const currentStep = steps[currentStepIndex];
  const progressPercentage = currentStepIndex >= steps.length ? 100 : (currentStepIndex / (steps.length - 1)) * 100;

  return (
    <div className="w-full bg-slate-900/50 p-4 rounded-t-2xl border-b border-slate-700 flex-shrink-0">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-semibold text-indigo-300">Progression du Workflow</h3>
        <span className="text-sm font-mono text-slate-400">
          Étape {Math.min(currentStepIndex + 1, steps.length)} / {steps.length}
        </span>
      </div>
      <div className="w-full bg-slate-700 rounded-full h-2.5 mb-3">
        <div
          className="bg-gradient-to-r from-purple-500 to-indigo-500 h-2.5 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>
      {currentStep ? (
         <div>
            <h4 className="font-bold text-slate-100">{currentStep.title}</h4>
            <p className="text-sm text-slate-400">{currentStep.description}</p>
         </div>
      ) : (
        <div>
            <h4 className="font-bold text-slate-100">Workflow Terminé</h4>
            <p className="text-sm text-slate-400">Le prompt final a été généré avec succès.</p>
        </div>
      )}
    </div>
  );
};
